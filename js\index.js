// gsap_animatejs
import "./animate.js";

// utils
import { changeTheme } from "./utils.js";
import {
  initEngine,
  initDefaultCamera,
  initDefaultLight,
  initPointLight,
  initAmbientLight,
  mouseMoveCtrlCamera,
  mouseMoveCtrlBabyHead,
  loadGlbModel,
  createRoundText,
  createStarField,
  createLeoConstellation
} from "./babylon.js";

// theme change
// const changeThemeBtn = document.getElementById("btn");
// changeThemeBtn.addEventListener("click", (e) => {
//     changeTheme(e,true);
// });

// babylon
// 初始化3D引擎
let engine = initEngine();
// 创建一个场景
let scene = new BABYLON.Scene(engine);
// 初始化全局光照
let globalLight = initDefaultLight(scene)
// 初始背景色设置
scene.clearColor = new BABYLON.Color4(0.05, 0.02, 0.15, 1); // 深紫色背景
// 初始化相机
let defaultCameraInstance = initDefaultCamera(scene);

// 第一章
// 存储baby模型和头部节点的引用
let babyRootNode = null;
let babyHeadNode = null;

// 加载婴儿模型
let babyModelInstance = loadGlbModel("baby", "./glb/", "baby.glb", scene, 0.06, new BABYLON.Vector3(0.14776839553400745, 0.9447594130096814, 0.003379749188015162))
  .then((babyModel) => {
    if (babyModel) {
      console.log("Baby模型加载成功", babyModel);
      babyRootNode = babyModel; // 保存根节点引用

      // 打印所有子节点名称，帮助找到头部节点
      console.log("Baby模型的所有子节点:");
      babyModel.getChildMeshes().forEach((mesh, index) => {
        console.log(`${index}: ${mesh.name}`);
      });

      // // 也检查所有变换节点
      // console.log("Baby模型的所有变换节点:");
      // babyModel.getChildTransformNodes().forEach((node, index) => {
      //   console.log(`${index}: ${node.name}`);
      // });

      // 尝试找到头部节点
      babyHeadNode = scene.getMeshByName("babycorpo_obj_1");
      console.log(babyHeadNode)
      babyHeadNode.sideOrientation = 1;

      return babyModel; // 返回模型以便链式调用
    }
  })
  .catch((error) => {
    console.error("Baby模型加载出错:", error);
  });
// 加载地球模型
let earthModelInstance = loadGlbModel('root', './glb/', 'earth_cartoon.glb', scene, 10, null,
  new BABYLON.Vector3(0, -18, 0)
).then((earthModel) => {
  if (earthModel) {
    // 使用GSAP创建顺时针旋转动画
    gsap.to(earthModel.rotation, {
      duration: 60, // 20秒完成一圈
      y: Math.PI * -2, // 绕Y轴旋转360度（2π弧度）
      ease: "none", // 线性动画，匀速旋转
      repeat: -1, // 无限循环
      onComplete: function () {
        // 重置旋转角度，避免数值过大
        earthModel.rotation.y = 0;
      }
    });
  } else {
    console.error("地球模型加载失败");
  }
}).catch((error) => {
  console.error("地球模型加载出错:", error);
});
// 初始化光源,并调整光源位置 - 降低亮度以突出星空
let pointLightInstance = initPointLight(
  scene,
  0.3, // 降低光照强度
  new BABYLON.Vector3(
    -1.035673975944519,
    0.06568589806556702,
    0.9425110220909119
  )
);

// 添加环境光 - 为中心模型提供柔和照明，不影响星空效果
let ambientLightInstance = initAmbientLight(
  scene,
  0.4, // 适中的环境光强度
  new BABYLON.Color3(0.7, 0.8, 0.9) // 略带蓝色的柔和光线
);
// 加载文字圆环并添加旋转动画
const textRing = createRoundText({
  texts: ["丁", "丑", "牛", "年", "涧", "下", "水", "天", "生", "地", "和"],
  radius: 5,
  scene: scene,
  y: 0,
  textOptions: { size: 1, depth: 0.1, resolution: 64 },
  animationOptions: {
    duration: 30, // 30秒完成一圈旋转
    direction: 1   // 1为顺时针，-1为逆时针
  }
});
// 加载宇宙星空背景 - 锐利的星光点缀深空
createStarField(scene, 1200, 150);

// 在右上角创建狮子座星座图
const leoConstellation = createLeoConstellation(
  scene,
  new BABYLON.Vector3(-33, 11.799604415893555, -35.12043762207031),
  0.6
);
leoConstellation.container.scaling = new BABYLON.Vector3(0.6, 0.6, 0.6);
leoConstellation.container.rotation = new BABYLON.Vector3(0, 0, -0.23280720505619254);
leoConstellation.container.position = new BABYLON.Vector3(-33.76450729370117, 8.575263977050781, -35.12043762207031);


// 调试层
scene.debugLayer.show();

engine.runRenderLoop(() => {
  return scene && scene?.render();
});

// 监听鼠标移动
window.addEventListener("mousemove", (e) => {
  // 鼠标移动控制相机的平滑移动
  // mouseMoveCtrlCamera(e, defaultCameraInstance);

  // 控制baby的脑袋随着鼠标移动
  if (babyHeadNode) {
    // console.log(babyHeadNode.rotation)
    mouseMoveCtrlBabyHead(e, babyHeadNode);
  }
});
