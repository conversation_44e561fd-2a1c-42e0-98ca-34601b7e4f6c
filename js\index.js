// gsap_animatejs
import "./animate.js";

// utils
import { changeTheme } from "./utils.js";
import {
  initEngine,
  initDefaultCamera,
  initDefaultLight,
  initPointLight,
  initAmbientLight,
  mouseMoveCtrlCamera,
  mouseMoveCtrlBabyHead,
  loadGlbModel,
  createRoundText,
  createStarField,
  createLeoConstellation,
  createVolumetricCloud,
  createMultipleClouds
} from "./babylon.js";

// theme change
// const changeThemeBtn = document.getElementById("btn");
// changeThemeBtn.addEventListener("click", (e) => {
//     changeTheme(e,true);
// });

// babylon
// 初始化3D引擎
let engine = initEngine();
// 创建一个场景
let scene = new BABYLON.Scene(engine);
// 初始化全局光照
let globalLight = initDefaultLight(scene)
// 初始背景色设置
scene.clearColor = new BABYLON.Color4(0.05, 0.02, 0.15, 1); // 深紫色背景
// 初始化相机
let defaultCameraInstance = initDefaultCamera(scene);

// 第一章
// 存储baby模型和头部节点的引用
let babyRootNode = null;
let babyHeadNode = null;
let babyHeadInitialRotation = null; // 保存头部的初始旋转

// 加载婴儿模型
let babyModelInstance = loadGlbModel("baby", "./glb/", "baby.glb", scene, 0.06, new BABYLON.Vector3(0.14776839553400745, 0.9447594130096814, 0.003379749188015162))
  .then((babyModel) => {
    if (babyModel) {
      console.log("Baby模型加载成功", babyModel);
      babyRootNode = babyModel; // 保存根节点引用

      // 打印所有子节点名称，帮助找到头部节点
      // console.log("Baby模型的所有子节点:");
      // babyModel.getChildMeshes().forEach((mesh, index) => {
        // console.log(`${index}: ${mesh.name}`);
      // });

      // 尝试找到头部节点
      babyHeadNode = scene.getMeshByName("babytesta_obj_1__0");
      if (babyHeadNode) {
        babyHeadNode.sideOrientation = 1;

        // 保存初始的rotationQuaternion
        if (babyHeadNode.rotationQuaternion) {
          babyHeadInitialRotation = babyHeadNode.rotationQuaternion.clone();
          console.log("保存了头部初始旋转:", babyHeadInitialRotation);
        } else {
          // 如果没有rotationQuaternion，从rotation创建一个
          babyHeadInitialRotation = BABYLON.Quaternion.FromEulerAngles(
            babyHeadNode.rotation.x,
            babyHeadNode.rotation.y,
            babyHeadNode.rotation.z
          );
          console.log("从欧拉角创建了初始旋转:", babyHeadInitialRotation);
        }

        // 清空rotationQuaternion，使用欧拉角控制
        babyHeadNode.rotationQuaternion = null;
      } else {
        console.error("未找到头部节点: babytesta_obj_1__0");
      }

      return babyModel; // 返回模型以便链式调用
    }
  })
  .catch((error) => {
    console.error("Baby模型加载出错:", error);
  });
// 加载地球模型
let earthModelInstance = loadGlbModel('root', './glb/', 'earth_cartoon.glb', scene, 10, null,
  new BABYLON.Vector3(0, -18, 0)
).then((earthModel) => {
  if (earthModel) {
    // 修改数目和云的材质
    let materialNameList = ['nube','vegetacin']
    materialNameList.forEach(item=>{
      let material = scene.getMaterialByName(item);
      material.wireframe = true;
    })
    // 使用GSAP创建顺时针旋转动画
    gsap.to(earthModel.rotation, {
      duration: 60, // n秒完成一圈
      y: Math.PI * -2, // 绕Y轴旋转360度（2π弧度）
      ease: "none", // 线性动画，匀速旋转
      repeat: -1, // 无限循环
      onComplete: function () {
        // 重置旋转角度，避免数值过大
        earthModel.rotation.y = 0;
      }
    });
  } else {
    console.error("地球模型加载失败");
  }
}).catch((error) => {
  console.error("地球模型加载出错:", error);
});
// 初始化光源,并调整光源位置 - 降低亮度以突出星空
let pointLightInstance = initPointLight(
  scene,
  0.3, // 降低光照强度
  new BABYLON.Vector3(
    -1.035673975944519,
    0.06568589806556702,
    0.9425110220909119
  )
);

// 添加环境光 - 为中心模型提供柔和照明，不影响星空效果
let ambientLightInstance = initAmbientLight(
  scene,
  0.4, // 适中的环境光强度
  new BABYLON.Color3(0.7, 0.8, 0.9) // 略带蓝色的柔和光线
);
// 加载文字圆环并添加旋转动画
const textRing = createRoundText({
  texts: ["丁", "丑", "牛", "年", "涧", "下", "水", "天", "生", "地", "和"],
  radius: 5,
  scene: scene,
  y: 0,
  textOptions: { size: 1, depth: 0.1, resolution: 64 },
  animationOptions: {
    duration: 30, // 30秒完成一圈旋转
    direction: 1   // 1为顺时针，-1为逆时针
  }
});
// 加载宇宙星空背景 - 锐利的星光点缀深空
createStarField(scene, 1200, 150);

// 创建云朵
const cloudsConfig = [
  {
    name: "cloud1",
    position: new BABYLON.Vector3(-28, 8, -2),
    particleSize: { min: 4, max: 8 },
    particleCount: 100, // 大幅减少粒子数量
    color: {
      color1: new BABYLON.Color4(1.0, 1.0, 1.0, 0.4),
      color2: new BABYLON.Color4(0.9, 0.9, 0.95, 0.5),
      colorDead: new BABYLON.Color4(0.8, 0.8, 0.85, 0.3)
    },
    cloudSize: { width: 15, height: 6, depth: 10 }
  },
  {
    name: "cloud2",
    position: new BABYLON.Vector3(-26, -16, -2),
    particleSize: { min: 3, max: 6 },
    particleCount: 80,
    color: {
      color1: new BABYLON.Color4(0.9, 0.95, 1.0, 0.35),
      color2: new BABYLON.Color4(0.8, 0.85, 0.9, 0.45),
      colorDead: new BABYLON.Color4(0.7, 0.75, 0.8, 0.25)
    },
    cloudSize: { width: 12, height: 5, depth: 8 }
  }
];
const clouds = createMultipleClouds(cloudsConfig, scene);

// 检测GSAP和ScrollTrigger加载状态
console.log("GSAP检测:", typeof gsap !== 'undefined' ? "已加载" : "未加载");
console.log("ScrollTrigger检测:", typeof ScrollTrigger !== 'undefined' ? "已加载" : "未加载");

// 等待一下确保所有资源加载完成
setTimeout(() => {
  console.log("延迟检测 - GSAP:", typeof gsap !== 'undefined' ? "已加载" : "未加载");
  console.log("延迟检测 - ScrollTrigger:", typeof ScrollTrigger !== 'undefined' ? "已加载" : "未加载");

  // 使用GSAP ScrollTrigger控制云朵的X轴位置
  if (typeof gsap !== 'undefined') {
    // 尝试注册ScrollTrigger插件
    try {
      if (typeof ScrollTrigger !== 'undefined') {
        gsap.registerPlugin(ScrollTrigger);
        console.log("ScrollTrigger插件注册成功");
      } else {
        console.error("ScrollTrigger未定义，尝试从gsap对象获取");
        if (gsap.ScrollTrigger) {
          gsap.registerPlugin(gsap.ScrollTrigger);
          console.log("从gsap对象注册ScrollTrigger成功");
        }
      }
    } catch (error) {
      console.error("ScrollTrigger注册失败:", error);
    }

    // 测试setPosition方法（移动整个云朵）
    console.log("测试云朵setPosition方法...");
    try {
      clouds[0].setPosition(new BABYLON.Vector3(-25, 8, -2));
      console.log("云朵1 setPosition测试成功，当前位置:", clouds[0].getPosition());
      console.log("云朵1容器位置:", clouds[0].cloudContainer.position);
      console.log("云朵1发射器位置:", clouds[0].emitter.position);
    } catch (error) {
      console.error("云朵1 setPosition测试失败:", error);
    }

    // 创建滚动监听
    if (gsap.ScrollTrigger || (typeof ScrollTrigger !== 'undefined')) {
      const ST = gsap.ScrollTrigger || ScrollTrigger;

      console.log("开始创建ScrollTrigger...");

      // 云朵1的滚动动画
      ST.create({
        trigger: "body",
        start: "top top",
        end: "bottom bottom",
        scrub: 1,
        onUpdate: function(self) {
          const progress = self.progress;
          const newX = -28 + (40 * progress); // 从-28移动到12
          console.log("滚动进度:", progress.toFixed(2), "云朵1新X位置:", newX.toFixed(2));

          try {
            // 移动整个云朵容器，包括所有已发射的粒子
            clouds[0].setPosition(new BABYLON.Vector3(newX, 8, -2));
            console.log("云朵1整体移动成功，容器位置:", clouds[0].getPosition());
          } catch (error) {
            console.error("云朵1位置更新失败:", error);
          }
        },
        onStart: () => console.log("云朵1滚动动画开始"),
        onComplete: () => console.log("云朵1滚动动画完成")
      });

      // 云朵2的滚动动画
      ST.create({
        trigger: "body",
        start: "top top",
        end: "bottom bottom",
        scrub: 1.5,
        onUpdate: function(self) {
          const progress = self.progress;
          const newX = -26 + (45 * progress); // 从-26移动到19
          console.log("滚动进度:", progress.toFixed(2), "云朵2新X位置:", newX.toFixed(2));

          try {
            // 移动整个云朵容器，包括所有已发射的粒子
            clouds[1].setPosition(new BABYLON.Vector3(newX, -16, -2));
            console.log("云朵2整体移动成功，容器位置:", clouds[1].getPosition());
          } catch (error) {
            console.error("云朵2位置更新失败:", error);
          }
        },
        onStart: () => console.log("云朵2滚动动画开始"),
        onComplete: () => console.log("云朵2滚动动画完成")
      });

      console.log("ScrollTrigger动画创建完成");

    } else {
      console.error("ScrollTrigger仍然不可用");
    }

  } else {
    console.error("GSAP未加载，无法设置滚动动画");
  }
}, 1000); // 延迟1秒确保所有脚本加载完成

// 备用方案：原生滚动监听（如果GSAP ScrollTrigger失败）
console.log("设置原生滚动监听作为备用方案...");
window.addEventListener('scroll', () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
  const progress = Math.min(scrollTop / scrollHeight, 1);

  console.log("原生滚动 - 进度:", progress.toFixed(2), "scrollTop:", scrollTop, "scrollHeight:", scrollHeight);

  // 测试云朵整体位置更新
  if (clouds && clouds.length >= 2) {
    try {
      const cloud1X = -28 + (40 * progress);
      const cloud2X = -26 + (45 * progress);

      // 移动整个云朵容器，包括所有已发射的粒子
      clouds[0].setPosition(new BABYLON.Vector3(cloud1X, 8, -2));
      clouds[1].setPosition(new BABYLON.Vector3(cloud2X, -16, -2));

      // 每20次滚动事件打印一次位置
      if (Math.random() < 0.05) {
        console.log("原生滚动更新 - 云朵1整体位置:", clouds[0].getPosition());
        console.log("原生滚动更新 - 云朵2整体位置:", clouds[1].getPosition());
      }
    } catch (error) {
      console.error("原生滚动位置更新失败:", error);
    }
  }
});

// 在右上角创建狮子座星座图
const leoConstellation = createLeoConstellation(
  scene,
  new BABYLON.Vector3(-33, 11.799604415893555, -35.12043762207031),
  0.6
);
leoConstellation.container.scaling = new BABYLON.Vector3(0.75, 0.75, 0.75);
leoConstellation.container.rotation = new BABYLON.Vector3(0, 0, -0.23280720505619254);
leoConstellation.container.position = new BABYLON.Vector3(-33.76450729370117, 8.575263977050781, -35.12043762207031);


// 调试层
// scene.debugLayer.show();

engine.runRenderLoop(() => {
  return scene && scene?.render();
});

// 监听鼠标移动
window.addEventListener("mousemove", (e) => {
  // 鼠标移动控制相机的平滑移动
  mouseMoveCtrlCamera(e, defaultCameraInstance);

  // 控制baby的脑袋随着鼠标移动
  if (babyHeadNode) {
    mouseMoveCtrlBabyHead(e, babyHeadNode, babyHeadInitialRotation);
  }
});
