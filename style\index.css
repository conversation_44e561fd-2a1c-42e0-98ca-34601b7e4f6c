:root {
    --bg-color: #fff;
    background-color: var(--bg-color);
}

:root.dark {
    --bg-color: #000;
}

::view-transition-old(root),
::view-transition-new(root) {
    animation: none;
}

.plane {
    width: 100%;
    height: 100%;
    position: relative;
}

.first_plane .leftTop_text {
    display: none;
    position: fixed;
    left: 50px;
    top: 20px;
    font-size: 45px;
    color: #fff;
}

.first_plane .leftTop_text .text {
    margin: 30px 0;
}

.first_plane .leftBot_text {
    position: fixed;
    left: 50px;
    bottom: 20px;
    font-size: 45px;
    color: #fff;
}