let rootCanvas = document.getElementById("babylonRenderCanvas");

const initEngine = () => {
  let engine = new BABYLON.Engine(rootCanvas, true);
  console.log("==babylonjs引擎初始化完成==");
  return engine;
};

const initDefaultCamera = (scene) => {
  let camera = new BABYLON.ArcRotateCamera(
    "defaultCamera",
    1.6041148277316222,
    1.4291006311487884,
    30,
    new BABYLON.Vector3(0, 0, 0),
    scene
  );
  camera.maxZ = 1000;
  camera.minZ = 0;
  //   调试用
  // camera.attachControl(rootCanvas, false);
  return camera;
};

const initDefaultLight = (scene, intensity = 1) => {
  // 创建一个半球光，模拟太阳光
  const light = new BABYLON.HemisphericLight(
    "defaultLight",
    new BABYLON.Vector3(0, 1, 0),
    scene
  );
  // 设置光的强度
  light.intensity = intensity;
  return light;
};

// 环境光函数 - 为中心模型提供柔和的环境照明
const initAmbientLight = (scene, intensity = 0.3, color = new BABYLON.Color3(0.8, 0.9, 1.0)) => {
  // 创建环境光，提供均匀的基础照明
  const ambientLight = new BABYLON.HemisphericLight(
    "ambientLight",
    new BABYLON.Vector3(0, 1, 0),
    scene
  );

  // 设置柔和的环境光颜色（略带蓝色，模拟宇宙环境）
  ambientLight.diffuse = color;
  ambientLight.specular = new BABYLON.Color3(0.1, 0.1, 0.1); // 很低的镜面反射
  ambientLight.groundColor = new BABYLON.Color3(0.2, 0.2, 0.3); // 地面反射带点蓝色

  // 设置适中的强度，既能照亮模型又不影响星空效果
  ambientLight.intensity = intensity;

  console.log(`环境光创建完成，强度: ${intensity}`);
  return ambientLight;
};
const initPointLight = (scene, intensity = 1, pos) => {
  const light = new BABYLON.PointLight(
    "pointLight",
    new BABYLON.Vector3(0, 1, 0),
    scene
  );
  light.diffuse = new BABYLON.Color3(1, 1, 1); // 设置漫反射颜色为白色
  light.specular = new BABYLON.Color3(1, 1, 1); // 设置镜面反射颜色为白色
  light.groundColor = new BABYLON.Color3(1, 1, 1); // 设置地面反射颜色为白色
  // 设置光的强度
  light.intensity = intensity;
  light.position = pos || new BABYLON.Vector3(0, 0, 0);
  return light;
};
/**
 * 通过设置弧形相机的target来控制相机移动
 * @param {*} e
 * @param {*} camera
 */
const mouseMoveCtrlCamera = (e, camera) => {
  const innerWidth = window.innerWidth;
  const innerHeight = window.innerHeight;
  // 归一化到 [-1, 1]
  let x = (e.clientX / innerWidth) * 2 - 1;
  let y = (e.clientY / innerHeight) * 2 - 1;

  // 设定 target 的变化范围，比如在 x: [-5, 5], y: [-5, 5] 之间
  let range = 3;
  let targetX = x * range;
  let targetY = y * range;
  let targetZ = 0; // 你可以根据需要调整z轴
  //gsap缓动
  gsap.to(camera.target, {
    x: targetX,
    y: targetY,
    z: targetZ,
    duration: 0.5,
  });
};

/**
 * 控制baby头部左右跟随鼠标移动
 * @param {*} e 鼠标事件
 * @param {*} babyHead baby头部节点
 * @param {*} initialRotation 初始的rotationQuaternion
 */
const mouseMoveCtrlBabyHead = (e, babyHead, initialRotation = null) => {
  if (!babyHead) {
    return;
  }

  const innerWidth = window.innerWidth;

  // 归一化到 [-1, 1]，只使用X轴（左右移动）
  let x = (e.clientX / innerWidth) * 2 - 1;

  // 设定头部旋转的角度范围（弧度）
  let rotationRange = 0.5; // 约17度的旋转范围
  let rotationY = x * rotationRange; // 左右转头

  if (initialRotation) {
    // 如果有初始旋转，在初始旋转的基础上应用鼠标控制
    // 创建鼠标控制的旋转四元数（只有Y轴旋转）
    const mouseRotationQuaternion = BABYLON.Quaternion.FromEulerAngles(0, rotationY, 0);

    // 将初始旋转和鼠标旋转组合
    const finalRotation = mouseRotationQuaternion.multiply(initialRotation);

    // 转换为欧拉角应用到节点
    const eulerAngles = finalRotation.toEulerAngles();

    gsap.to(babyHead.rotation, {
      x: eulerAngles.x,
      y: eulerAngles.y,
      z: eulerAngles.z,
      duration: 0.1,
      ease: "power2.out",
    });
  } else {
    // 没有初始旋转时的原始逻辑（只有Y轴旋转）
    gsap.to(babyHead.rotation, {
      y: rotationY,
      duration: 0.1,
      ease: "power2.out",
    });
  }
};

/**
 * 创建简化的云朵（使用本地图片，高性能）
 * @param {Object} options 云朵配置选项
 * @param {BABYLON.Scene} scene 场景
 * @returns {Object} 云朵对象
 */
const createVolumetricCloud = (options, scene) => {
  const {
    name = "cloud",
    position = new BABYLON.Vector3(0, 5, 0),
    particleSize = { min: 3, max: 6 },
    color = {
      color1: new BABYLON.Color4(1.0, 1.0, 1.0, 0.3),
      color2: new BABYLON.Color4(0.9, 0.9, 0.95, 0.4),
      colorDead: new BABYLON.Color4(0.8, 0.8, 0.85, 0.2)
    },
    particleCount = 200, // 大幅减少粒子数量
    cloudSize = { width: 10, height: 5, depth: 10 },
    animationSpeed = 0.01
  } = options;

  // 创建云朵容器节点（用于整体移动）
  const cloudContainer = new BABYLON.TransformNode(`${name}_container`, scene);
  cloudContainer.position = position.clone();

  // 创建云朵的发射器（不可见的盒子，相对于容器的本地位置）
  const emitter = BABYLON.Mesh.CreateBox(`${name}_emitter`, 0.01, scene);
  emitter.position = new BABYLON.Vector3(0, 0, 0); // 相对于容器的本地位置
  emitter.visibility = 0;
  emitter.parent = cloudContainer; // 设置父节点

  // 使用本地smoke_15图片
  const fogTexture = new BABYLON.Texture("./assets/smoke_15.png", scene);

  // 创建粒子系统（大幅减少粒子数量）
  const particleSystem = new BABYLON.ParticleSystem(`${name}_particles`, particleCount, scene);

  // 设置发射器和纹理
  particleSystem.emitter = emitter;
  particleSystem.particleTexture = fogTexture;

  // 设置世界偏移，让整个粒子云跟随容器移动
  particleSystem.worldOffset = new BABYLON.Vector3(0, 0, 0);

  // 设置发射区域（云朵的形状和大小）
  particleSystem.minEmitBox = new BABYLON.Vector3(-cloudSize.width/2, -cloudSize.height/2, -cloudSize.depth/2);
  particleSystem.maxEmitBox = new BABYLON.Vector3(cloudSize.width/2, cloudSize.height/2, cloudSize.depth/2);

  // 设置颜色
  particleSystem.color1 = color.color1;
  particleSystem.color2 = color.color2;
  particleSystem.colorDead = color.colorDead;

  // 设置粒子大小
  particleSystem.minSize = particleSize.min;
  particleSystem.maxSize = particleSize.max;

  // 设置粒子生命周期（更长的生命周期，减少重新生成）
  particleSystem.minLifeTime = 20;
  particleSystem.maxLifeTime = 30;

  // 设置发射速率（降低发射速率）
  particleSystem.emitRate = 20;

  // 设置混合模式
  particleSystem.blendMode = BABYLON.ParticleSystem.BLENDMODE_ALPHABLEND;

  // 设置重力和方向（静态云朵）
  particleSystem.gravity = new BABYLON.Vector3(0, 0, 0);
  particleSystem.direction1 = new BABYLON.Vector3(-0.1, 0, -0.1);
  particleSystem.direction2 = new BABYLON.Vector3(0.1, 0, 0.1);

  // 设置发射力度（很小的力度）
  particleSystem.minEmitPower = 0.1;
  particleSystem.maxEmitPower = 0.3;

  // 设置旋转（轻微旋转）
  particleSystem.minAngularSpeed = -0.5;
  particleSystem.maxAngularSpeed = 0.5;

  // 设置更新速度
  particleSystem.updateSpeed = animationSpeed;

  // 启动粒子系统
  particleSystem.start();
  return {
    particleSystem,
    emitter,
    cloudContainer, // 新增：云朵容器节点
    // 提供控制方法 - 移动整个云朵
    setPosition: (newPosition) => {
      cloudContainer.position = newPosition; // 移动容器，整个云朵跟随移动
    },
    // 获取当前位置
    getPosition: () => {
      return cloudContainer.position;
    },
    setColor: (newColor) => {
      particleSystem.color1 = newColor.color1 || particleSystem.color1;
      particleSystem.color2 = newColor.color2 || particleSystem.color2;
      particleSystem.colorDead = newColor.colorDead || particleSystem.colorDead;
    },
    dispose: () => {
      particleSystem.dispose();
      emitter.dispose();
      fogTexture.dispose();
      cloudContainer.dispose();
    }
  };
};

/**
 * 创建多片简化云朵（高性能版本）
 * @param {Array} cloudsConfig 云朵配置数组
 * @param {BABYLON.Scene} scene 场景
 * @returns {Array} 云朵对象数组
 */
const createMultipleClouds = (cloudsConfig, scene) => {
  const clouds = [];

  cloudsConfig.forEach((config, index) => {
    // 简化配置，减少随机计算
    const simplifiedConfig = {
      name: config.name || `cloud_${index}`,
      position: config.position,
      particleSize: config.particleSize,
      color: config.color,
      particleCount: config.particleCount || 150, // 进一步减少粒子数量
      cloudSize: config.cloudSize,
      animationSpeed: config.animationSpeed || 0.01
    };

    const cloud = createVolumetricCloud(simplifiedConfig, scene);
    clouds.push(cloud);
  });

  return clouds;
};

/**
 * 在圆环上创建3D文字并添加旋转动画
 * @param {Object} options
 * @param {string[]} options.texts 文字数组
 * @param {number} options.radius 圆环半径
 * @param {BABYLON.Scene} options.scene 场景对象
 * @param {number} [options.y=0] y轴高度
 * @param {Object} [options.textOptions] 文字mesh参数
 * @param {Object} [options.animationOptions] 动画参数
 */
const createRoundText = async ({
  texts,
  radius,
  scene,
  y = 0,
  textOptions = { size: 1, depth: 0.2 },
  animationOptions = { duration: 20, direction: 1 }
}) => {
  const count = texts.length;
  const meshes = [];

  // 创建一个父容器用于整体旋转
  const textContainer = new BABYLON.TransformNode("textContainer", scene);
  textContainer.position.y = y;

  for (let i = 0; i < count; i++) {
    const angle = (2 * Math.PI * i) / count;
    const x = Math.cos(angle) * radius;
    const z = Math.sin(angle) * radius;

    // 创建3D文字
    const fontData = await (await fetch("../font/fontForBabylon.json")).json();
    const textMesh = BABYLON.MeshBuilder.CreateText(
      `text_${i}`,
      texts[i],
      fontData,
      textOptions
    );

    // 创建中国古典烫金工艺材质
    const goldMaterial = new BABYLON.StandardMaterial(`goldMaterial_${i}`, scene);

    // 古典金色配色 - 模拟真实烫金效果
    goldMaterial.diffuseColor = new BABYLON.Color3(0.9, 0.7, 0.2); // 深金色漫反射
    goldMaterial.specularColor = new BABYLON.Color3(1.0, 0.95, 0.7); // 亮金色高光
    goldMaterial.emissiveColor = new BABYLON.Color3(1.2, 0.8, 0.3); // 强烈金色自发光

    // 烫金工艺质感设置
    goldMaterial.specularPower = 256; // 极高光锐度，模拟抛光金属
    goldMaterial.roughness = 0.05; // 极低粗糙度，镜面效果
    goldMaterial.metallicFactor = 1.0; // 完全金属质感

    // 古典烫金特效
    goldMaterial.disableLighting = false; // 保留光照获得立体感
    goldMaterial.useSpecularOverAlpha = true; // 高光覆盖
    goldMaterial.invertNormalMapX = false;
    goldMaterial.invertNormalMapY = false;

    // 增强自发光强度，模拟烫金的光泽
    goldMaterial.emissiveFresnelParameters = new BABYLON.FresnelParameters();
    goldMaterial.emissiveFresnelParameters.bias = 0.1;
    goldMaterial.emissiveFresnelParameters.power = 0.5;
    goldMaterial.emissiveFresnelParameters.leftColor = new BABYLON.Color3(1.5, 1.0, 0.4);
    goldMaterial.emissiveFresnelParameters.rightColor = new BABYLON.Color3(0.8, 0.6, 0.2);

    // 应用材质到文字
    textMesh.material = goldMaterial;

    // 设置文字位置
    textMesh.position = new BABYLON.Vector3(x, 0, z);
    // 让文字朝向圆心
    textMesh.lookAt(new BABYLON.Vector3(0, 0, 0));

    // 将文字设为容器的子对象
    textMesh.parent = textContainer;
    meshes.push(textMesh);
  }

  // 使用GSAP创建无限旋转动画
  gsap.to(textContainer.rotation, {
    y: animationOptions.direction * Math.PI * 2, // 旋转360度
    duration: animationOptions.duration, // 旋转周期
    ease: "none", // 线性动画，匀速旋转
    repeat: -1, // 无限重复
    onComplete: function () {
      // 重置旋转角度避免数值过大
      textContainer.rotation.y = 0;
    }
  });

  console.log(`文字圆环创建完成，包含 ${count} 个文字，旋转周期: ${animationOptions.duration}秒`);

  return {
    container: textContainer,
    meshes: meshes,
    // 提供控制动画的方法
    pauseAnimation: () => gsap.globalTimeline.pause(),
    resumeAnimation: () => gsap.globalTimeline.resume(),
    setSpeed: (speed) => gsap.globalTimeline.timeScale(speed)
  };
};

const loadGlbModel = (name, filePath, modelPath, scene, scale, rotation, pos) => {
  return BABYLON.SceneLoader.ImportMeshAsync(
    name,
    filePath,
    modelPath,
    scene,
    (evt) => {
      console.log(evt);
    }
  ).then((model) => {
    console.log("加载完成", model);
    let rootNode = scene.getNodeByName(name);
    rootNode.scaling = new BABYLON.Vector3(scale, scale, scale);
    rootNode.rotation = rotation || new BABYLON.Quaternion(0, 0, 0, 0);
    rootNode.position = pos || new BABYLON.Vector3(0, 0, 0);

    // 返回根节点以便后续操作
    return rootNode;
  });
};
// 星空背景 - 使用粒子系统
function createStarField(scene, starCount = 1000, areaSize = 50) {
  console.log(`开始创建星空粒子系统，星星数量: ${starCount}, 区域大小: ${areaSize}`);

  try {
    // 创建粒子系统
    const particleSystem = new BABYLON.ParticleSystem("starParticles", starCount, scene);

    // 创建锐利的星光纹理 - 类似真实宇宙中的星星
    const starTexture = new BABYLON.DynamicTexture("starTexture", 64, scene);
    const context = starTexture.getContext();
    const size = starTexture.getSize();

    // 清除背景
    context.fillStyle = "rgba(0, 0, 0, 0)";
    context.fillRect(0, 0, size.width, size.height);

    // 绘制锐利的星光 - 中心亮点 + 十字光芒
    const centerX = size.width / 2;
    const centerY = size.height / 2;

    // 1. 绘制中心亮点（小而锐利）- 金橙色
    context.fillStyle = "rgba(255, 200, 100, 1)";
    context.fillRect(centerX - 1, centerY - 1, 3, 3);

    // 2. 绘制十字光芒（星光特有的衍射效果）- 渐变金色
    context.fillStyle = "rgba(255, 180, 80, 0.9)";
    // 水平光芒
    context.fillRect(centerX - 20, centerY, 40, 1);
    context.fillStyle = "rgba(255, 160, 60, 0.7)";
    context.fillRect(centerX - 15, centerY - 1, 30, 3);

    // 垂直光芒
    context.fillStyle = "rgba(255, 180, 80, 0.9)";
    context.fillRect(centerX, centerY - 20, 1, 40);
    context.fillStyle = "rgba(255, 160, 60, 0.7)";
    context.fillRect(centerX - 1, centerY - 15, 3, 30);

    starTexture.update();

    particleSystem.particleTexture = starTexture;

    // 创建发射器（不可见的点）
    const emitter = BABYLON.MeshBuilder.CreateSphere("starEmitter", { diameter: 0.01 }, scene);
    emitter.isVisible = false;
    particleSystem.emitter = emitter;

    // 设置发射范围 - 宇宙深空背景，围绕中心模型
    particleSystem.minEmitBox = new BABYLON.Vector3(-areaSize * 2, -areaSize * 2, -areaSize * 3);
    particleSystem.maxEmitBox = new BABYLON.Vector3(areaSize * 2, areaSize * 2, -areaSize / 2);

    // 设置粒子属性 - 稍大一些的锐利星光点
    particleSystem.minSize = 0.6;
    particleSystem.maxSize = 1.4;
    particleSystem.minLifeTime = 999999; // 几乎永不消失
    particleSystem.maxLifeTime = 999999;
    particleSystem.emitRate = starCount; // 一次性发射所有粒子

    // 设置粒子速度为0（静止的星星）
    particleSystem.minEmitPower = 0;
    particleSystem.maxEmitPower = 0;
    particleSystem.updateSpeed = 0.001;

    // 设置重力为0
    particleSystem.gravity = new BABYLON.Vector3(0, 0, 0);

    // 设置金橙色星光颜色 - 温暖而有张力
    particleSystem.color1 = new BABYLON.Color4(3.5, 2.2, 1.0, 1.0); // 亮金橙色
    particleSystem.color2 = new BABYLON.Color4(3.0, 1.8, 0.8, 1.0); // 金色
    particleSystem.colorDead = new BABYLON.Color4(2.5, 1.5, 0.6, 1.0); // 暗金色

    // 设置混合模式为加法混合实现强发光效果
    particleSystem.blendMode = 1; // BLENDMODE_ONEONE 的值

    // 启动粒子系统
    particleSystem.start();

    console.log(`粒子系统启动完成`);

    // 添加真实的星光闪烁效果 - 模拟大气扰动和距离变化
    let time = 0;
    let twinklePhase1 = 0;
    let twinklePhase2 = 0;
    let twinklePhase3 = 0;

    scene.registerBeforeRender(() => {
      time += 0.016; // 60fps

      // 多层次闪烁模拟真实星光
      twinklePhase1 += 0.03 + Math.random() * 0.02; // 主要闪烁
      twinklePhase2 += 0.01 + Math.random() * 0.01; // 缓慢变化
      twinklePhase3 += 0.05 + Math.random() * 0.03; // 快速闪烁

      // 计算复合闪烁强度
      const mainTwinkle = Math.sin(twinklePhase1) * 0.4;
      const slowTwinkle = Math.sin(twinklePhase2) * 0.2;
      const fastTwinkle = Math.sin(twinklePhase3) * 0.3;

      const totalTwinkle = mainTwinkle + slowTwinkle + fastTwinkle;
      const baseIntensity = 1.8;
      const finalIntensity = Math.max(0.8, baseIntensity + totalTwinkle);

      // 应用金橙色星光闪烁效果
      particleSystem.color1 = new BABYLON.Color4(
        4.5 * finalIntensity,  // 红色分量 - 金色主体
        2.8 * finalIntensity,  // 绿色分量 - 橙色调
        1.2 * finalIntensity,  // 蓝色分量 - 少量蓝色
        1.0
      );
      particleSystem.color2 = new BABYLON.Color4(
        4.0 * finalIntensity,  // 红色分量
        2.4 * finalIntensity,  // 绿色分量
        1.0 * finalIntensity,  // 蓝色分量
        1.0
      );
    });

    console.log(`星空粒子系统创建完成，共 ${starCount} 颗金橙色发光星星`);
    return particleSystem;

  } catch (error) {
    console.error("创建星空粒子系统时出错:", error);
    return null;
  }
}

// 创建狮子座星座图
function createLeoConstellation(scene, position = new BABYLON.Vector3(30, 20, -40), scale = 1.0) {
  console.log("开始创建狮子座星座图");

  // 狮子座主要星星的相对位置（基于真实狮子座形状，间距扩大2.5倍）
  const leoStars = [
    { name: "Regulus", pos: [0, 0, 0], brightness: 1.0 }, // 轩辕十四（狮子座α星）
    { name: "Denebola", pos: [20, -5, 2.5], brightness: 0.8 }, // 五帝座一（狮子座β星）
    { name: "Algieba", pos: [-7.5, 5, -1.25], brightness: 0.7 }, // 轩辕十二（狮子座γ星）
    { name: "Zosma", pos: [12.5, 2.5, 1.25], brightness: 0.6 }, // 上相（狮子座δ星）
    { name: "Ras Elased Australis", pos: [-15, 0, -2.5], brightness: 0.5 }, // 狮子座ε星
    { name: "Adhafera", pos: [-10, 7.5, -2], brightness: 0.5 }, // 轩辕十一（狮子座ζ星）
    { name: "Al Jabhah", pos: [-5, 10, -2.5], brightness: 0.4 }, // 轩辕九（狮子座η星）
    { name: "Chertan", pos: [15, -2.5, 2], brightness: 0.4 }, // 右执法（狮子座θ星）
    { name: "Ras Elased Borealis", pos: [-17.5, 2.5, -3], brightness: 0.4 }, // 狮子座μ星
    { name: "Subra", pos: [-2.5, 7.5, -1.5], brightness: 0.3 }, // 狮子座ο星
  ];

  // 狮子座连线（构成狮子的形状）
  const leoConnections = [
    [0, 2], // Regulus - Algieba
    [2, 5], // Algieba - Adhafera
    [5, 6], // Adhafera - Al Jabhah
    [6, 9], // Al Jabhah - Subra
    [2, 4], // Algieba - Ras Elased Australis
    [4, 8], // Ras Elased Australis - Ras Elased Borealis
    [0, 3], // Regulus - Zosma
    [3, 7], // Zosma - Chertan
    [3, 1], // Zosma - Denebola
    [7, 1], // Chertan - Denebola
  ];

  // 创建容器
  const leoContainer = new BABYLON.TransformNode("leoConstellation", scene);
  leoContainer.position = position;
  leoContainer.scaling = new BABYLON.Vector3(scale, scale, scale);

  const stars = [];
  const lines = [];

  // 创建星星（使用更自然的方法）
  for (let i = 0; i < leoStars.length; i++) {
    const starData = leoStars[i];
    const star = createCustomStar(scene, starData, i);

    // 设置位置
    star.position = new BABYLON.Vector3(
      starData.pos[0],
      starData.pos[1],
      starData.pos[2]
    );

    star.parent = leoContainer;
    stars.push(star);
  }

  // 创建连线（使用管道几何体实现模糊边缘效果）
  for (let i = 0; i < leoConnections.length; i++) {
    const connection = leoConnections[i];
    const startStar = stars[connection[0]];
    const endStar = stars[connection[1]];

    // 创建路径点
    const path = [startStar.position, endStar.position];

    // 创建管道线条以获得更好的视觉效果
    const line = BABYLON.MeshBuilder.CreateTube(`leoLine_${i}`, {
      path: path,
      radius: 0.02,
      tessellation: 8
    }, scene);

    // 创建发光线条材质
    const lineMaterial = new BABYLON.StandardMaterial(`leoLineMat_${i}`, scene);
    lineMaterial.emissiveColor = new BABYLON.Color3(1.0, 1.0, 1.0); // 白色发光
    lineMaterial.diffuseColor = new BABYLON.Color3(0.8, 0.9, 1.0); // 淡蓝色边缘
    lineMaterial.disableLighting = true;
    lineMaterial.alpha = 0.8;
    lineMaterial.alphaMode = BABYLON.Engine.ALPHA_ADD;

    line.material = lineMaterial;
    line.parent = leoContainer;
    lines.push(line);
  }

  // 创建视频背景
  const videoBackground = createVideoBackground(scene, leoContainer);

  // 添加呼吸闪烁动画
  createBreathingAnimation(scene, stars, lines);

  console.log(`狮子座创建完成，包含 ${stars.length} 颗星星和 ${lines.length} 条连线，以及视频背景和呼吸动画`);

  return {
    container: leoContainer,
    stars: stars,
    lines: lines,
    videoBackground: videoBackground
  };
}

// 创建呼吸闪烁动画
function createBreathingAnimation(scene, stars, lines) {
  let time = 0;

  const breathingAnimation = () => {
    time += 0.02;

    // 计算呼吸效果的强度 (0.6 到 1.0 之间)
    const breathIntensity = 0.6 + 0.4 * (Math.sin(time) * 0.5 + 0.5);

    // 应用到所有星星
    stars.forEach((star) => {
      if (star.starData) {
        const brightness = star.starData.brightness;

        // 更新核心材质
        if (star.coreMaterial) {
          star.coreMaterial.emissiveColor = new BABYLON.Color3(
            brightness * breathIntensity,
            brightness * breathIntensity,
            brightness * breathIntensity
          );
        }

        // 更新光晕材质
        if (star.haloMaterial) {
          star.haloMaterial.emissiveColor = new BABYLON.Color3(
            0.7 * brightness * breathIntensity,
            0.8 * brightness * breathIntensity,
            1.0 * brightness * breathIntensity
          );
        }

        // 更新外晕材质
        if (star.glowMaterial) {
          star.glowMaterial.emissiveColor = new BABYLON.Color3(
            0.5 * brightness * breathIntensity,
            0.5 * brightness * breathIntensity,
            0.6 * brightness * breathIntensity
          );
        }
      }
    });

    // 应用到所有连线
    lines.forEach(line => {
      if (line.material && line.material.emissiveColor) {
        line.material.emissiveColor = new BABYLON.Color3(
          breathIntensity,
          breathIntensity,
          breathIntensity
        );
        // 淡蓝色边缘也跟随呼吸
        line.material.diffuseColor = new BABYLON.Color3(
          0.8 * breathIntensity,
          0.9 * breathIntensity,
          1.0 * breathIntensity
        );
      }
    });
  };

  // 注册动画到场景
  scene.registerBeforeRender(breathingAnimation);

  return breathingAnimation;
}

// 创建视频背景平面
function createVideoBackground(scene, parent) {
  console.log("开始创建视频背景");

  // 尝试创建视频背景，如果失败则使用动态纹理
  try {
    return createVideoTexture(scene, parent);
  } catch (error) {
    console.log("视频加载失败，使用动态纹理作为备用方案:", error);
    return createDynamicBackground(scene, parent);
  }
}

// 创建视频纹理背景
function createVideoTexture(scene, parent) {
  // 创建视频元素
  const videoElement = document.createElement("video");
  videoElement.src = "./assets/leoVideo.mp4"; // 使用现有的视频文件
  videoElement.loop = true;
  videoElement.muted = true; // 静音以允许自动播放
  videoElement.autoplay = true;
  videoElement.crossOrigin = "anonymous";

  // 设置视频样式（隐藏在DOM中）
  videoElement.style.display = "none";
  document.body.appendChild(videoElement);

  // 创建视频纹理
  const videoTexture = new BABYLON.VideoTexture("leoVideoTexture", videoElement, scene);

  // 创建背景平面
  const backgroundPlane = BABYLON.MeshBuilder.CreatePlane("leoVideoBackground", {
    width: 40,  // 调整宽度以覆盖星座区域
    height: 25  // 调整高度以覆盖星座区域
  }, scene);

  // 创建渐变遮罩纹理
  const maskTexture = createGradientMask(scene);

  // 创建视频材质
  const videoMaterial = new BABYLON.StandardMaterial("leoVideoMaterial", scene);
  videoMaterial.diffuseTexture = videoTexture;
  videoMaterial.emissiveTexture = videoTexture;
  videoMaterial.opacityTexture = maskTexture; // 使用渐变遮罩
  videoMaterial.emissiveColor = new BABYLON.Color3(0.2, 0.2, 0.3); // 降低发光强度，偏蓝紫色
  videoMaterial.alpha = 0.6; // 增加透明度，让视频更明显但不遮挡星座
  videoMaterial.backFaceCulling = false; // 双面显示

  // 添加混合模式以获得更好的融合效果
  videoMaterial.alphaMode = BABYLON.Engine.ALPHA_ADD;

  backgroundPlane.material = videoMaterial;

  // 定位视频背景（在星座后面，稍微偏移）
  backgroundPlane.position = new BABYLON.Vector3(0, 0, -8); // Z轴向后偏移
  backgroundPlane.rotation.y = Math.PI * 0.1; // 轻微旋转增加层次感
  backgroundPlane.parent = parent;

  // 播放视频
  videoElement.play().catch(error => {
    console.log("视频自动播放失败，可能需要用户交互:", error);

    // 添加点击事件来启动视频
    const playVideo = () => {
      videoElement.play();
      document.removeEventListener('click', playVideo);
      document.removeEventListener('touchstart', playVideo);
    };

    document.addEventListener('click', playVideo);
    document.addEventListener('touchstart', playVideo);
  });

  return {
    plane: backgroundPlane,
    videoElement: videoElement,
    texture: videoTexture,
    material: videoMaterial,
    type: "video"
  };
}

// 创建动态纹理背景（备用方案）
function createDynamicBackground(scene, parent) {
  console.log("创建动态纹理背景");

  // 创建动态纹理
  const dynamicTexture = new BABYLON.DynamicTexture("leoDynamicTexture", { width: 512, height: 512 }, scene);

  // 创建背景平面
  const backgroundPlane = BABYLON.MeshBuilder.CreatePlane("leoDynamicBackground", {
    width: 50,
    height: 55
  }, scene);

  // 创建渐变遮罩纹理
  const maskTexture = createGradientMask(scene);

  // 创建材质
  const dynamicMaterial = new BABYLON.StandardMaterial("leoDynamicMaterial", scene);
  dynamicMaterial.diffuseTexture = dynamicTexture;
  dynamicMaterial.emissiveTexture = dynamicTexture;
  dynamicMaterial.opacityTexture = maskTexture; // 使用渐变遮罩
  dynamicMaterial.emissiveColor = new BABYLON.Color3(0.3, 0.2, 0.4); // 紫色调
  dynamicMaterial.alpha = 0.5;
  dynamicMaterial.backFaceCulling = false;
  dynamicMaterial.alphaMode = BABYLON.Engine.ALPHA_ADD;

  backgroundPlane.material = dynamicMaterial;
  backgroundPlane.position = new BABYLON.Vector3(0, 0, -8);
  backgroundPlane.rotation.y = Math.PI * 0.1;
  backgroundPlane.parent = parent;

  // 创建动画效果
  let time = 0;
  const animateTexture = () => {
    time += 0.02;

    // 获取纹理的上下文
    const context = dynamicTexture.getContext();
    const size = dynamicTexture.getSize();

    // 清除画布
    context.fillStyle = `rgba(${Math.sin(time) * 20 + 30}, ${Math.cos(time * 0.7) * 20 + 40}, ${Math.sin(time * 0.5) * 30 + 60}, 0.8)`;
    context.fillRect(0, 0, size.width, size.height);

    // 绘制流动的星云效果
    for (let i = 0; i < 20; i++) {
      const x = (Math.sin(time + i) * 0.3 + 0.5) * size.width;
      const y = (Math.cos(time * 0.8 + i) * 0.3 + 0.5) * size.height;
      const radius = Math.sin(time + i * 0.5) * 20 + 30;

      const gradient = context.createRadialGradient(x, y, 0, x, y, radius);
      gradient.addColorStop(0, `rgba(255, 215, 100, ${0.3 + Math.sin(time + i) * 0.2})`);
      gradient.addColorStop(1, 'rgba(255, 215, 100, 0)');

      context.fillStyle = gradient;
      context.beginPath();
      context.arc(x, y, radius, 0, Math.PI * 2);
      context.fill();
    }

    // 更新纹理
    dynamicTexture.update();
  };

  // 启动动画循环
  scene.registerBeforeRender(animateTexture);

  return {
    plane: backgroundPlane,
    texture: dynamicTexture,
    material: dynamicMaterial,
    type: "dynamic",
    stopAnimation: () => scene.unregisterBeforeRender(animateTexture)
  };
}

// 创建矩形边缘渐变遮罩纹理
function createGradientMask(scene) {
  const maskTexture = new BABYLON.DynamicTexture("gradientMask", { width: 512, height: 512 }, scene);
  const context = maskTexture.getContext();
  const size = maskTexture.getSize();

  // 清除画布
  context.clearRect(0, 0, size.width, size.height);

  // 创建矩形边缘渐变效果
  const imageData = context.createImageData(size.width, size.height);
  const data = imageData.data;

  // 渐变边缘的宽度（像素）
  const fadeWidth = size.width * 0.15; // 边缘渐变区域占总宽度的15%
  const fadeHeight = size.height * 0.15; // 边缘渐变区域占总高度的15%

  for (let y = 0; y < size.height; y++) {
    for (let x = 0; x < size.width; x++) {
      const index = (y * size.width + x) * 4;

      // 计算到边缘的距离
      const distanceFromLeft = x;
      const distanceFromRight = size.width - x;
      const distanceFromTop = y;
      const distanceFromBottom = size.height - y;

      // 找到最近的边缘距离
      const minDistanceX = Math.min(distanceFromLeft, distanceFromRight);
      const minDistanceY = Math.min(distanceFromTop, distanceFromBottom);
      const minDistance = Math.min(minDistanceX, minDistanceY);

      // 创建平滑的边缘渐变
      let alpha;
      if (minDistance >= fadeWidth && minDistanceY >= fadeHeight) {
        alpha = 255; // 中心区域完全不透明
      } else {
        // 在渐变区域内
        const fadeFactorX = minDistanceX < fadeWidth ? minDistanceX / fadeWidth : 1;
        const fadeFactorY = minDistanceY < fadeHeight ? minDistanceY / fadeHeight : 1;
        const fadeFactor = Math.min(fadeFactorX, fadeFactorY);

        // 使用平滑的渐变曲线
        const smoothFade = fadeFactor * fadeFactor * (3 - 2 * fadeFactor); // smoothstep函数
        alpha = Math.max(0, 255 * smoothFade);
      }

      // 设置像素值 (白色，透明度由alpha控制)
      data[index] = 255;     // R
      data[index + 1] = 255; // G
      data[index + 2] = 255; // B
      data[index + 3] = alpha; // A
    }
  }

  context.putImageData(imageData, 0, 0);
  maskTexture.update();

  return maskTexture;
}

// 创建自定义星星的函数
function createCustomStar(scene, starData, index) {
  const size = 0.8 + starData.brightness * 0.6;

  // 创建粒子系统
  const particleSystem = new BABYLON.ParticleSystem(`starParticles_${index}`, 50, scene);

  // 创建发射器（一个小的不可见球体）
  const emitter = BABYLON.MeshBuilder.CreateSphere(`starEmitter_${index}`, {
    diameter: 0.1
  }, scene);
  emitter.isVisible = false;

  particleSystem.particleTexture = new BABYLON.Texture("./assets/leoStar.png", scene);
  particleSystem.emitter = emitter;

  // 配置粒子
  particleSystem.minEmitBox = new BABYLON.Vector3(0, 0, 0);
  particleSystem.maxEmitBox = new BABYLON.Vector3(0, 0, 0);

  particleSystem.color1 = new BABYLON.Color4(1, 1, 1, 1);
  particleSystem.color2 = new BABYLON.Color4(0.8, 0.9, 1, 1);
  particleSystem.colorDead = new BABYLON.Color4(0, 0, 0, 0);

  particleSystem.minSize = size * 0.5;
  particleSystem.maxSize = size;

  particleSystem.minLifeTime = 1;
  particleSystem.maxLifeTime = 1;

  particleSystem.emitRate = 10;

  particleSystem.blendMode = BABYLON.ParticleSystem.BLENDMODE_ADD;

  particleSystem.gravity = new BABYLON.Vector3(0, 0, 0);
  particleSystem.direction1 = new BABYLON.Vector3(0, 0, 0);
  particleSystem.direction2 = new BABYLON.Vector3(0, 0, 0);

  particleSystem.minAngularSpeed = 0;
  particleSystem.maxAngularSpeed = 0;

  particleSystem.minEmitPower = 0;
  particleSystem.maxEmitPower = 0;

  particleSystem.start();

  // 添加自定义属性
  emitter.particleSystem = particleSystem;
  emitter.starData = starData;

  return emitter;
}

const test = (scene) => {
  // Our built-in 'sphere' shape.
  const sphere = BABYLON.MeshBuilder.CreateSphere(
    "sphere",
    { diameter: 2, segments: 32 },
    scene
  );

  // Move the sphere upward 1/2 its height
  sphere.position.y = 0;

  // Our built-in 'ground' shape.
  const ground = BABYLON.MeshBuilder.CreateGround(
    "ground",
    { width: 16, height: 16 },
    scene
  );
};
export {
  initEngine,
  initDefaultCamera,
  initDefaultLight,
  initAmbientLight,
  mouseMoveCtrlCamera,
  mouseMoveCtrlBabyHead,
  loadGlbModel,
  initPointLight,
  createRoundText,
  createStarField,
  createLeoConstellation,
  createVolumetricCloud,
  createMultipleClouds,
  test,
};
