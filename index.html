<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- TODO:这个位置的title要改成动态+icon的 -->
    <title>Document</title>
    <link rel="stylesheet" href="./style/global.css">
    <link rel="stylesheet" href="./style/index.css">
</head>

<body>
    <!-- TODO:按钮位置和样式需要修改 -->
    <!-- <button id="btn">切换</button> -->
    <!-- 第一幕，baby -->
    <section class="first_plane plane">
        <div class="baby_container">
            <div class="leftTop_text">
                <p class="text">Hi !</p>
                <p class="text">I`am Othniel</p>
                <p class="text">A Web Developer</p>
                <p class="text">Welcome</p>
                <p class="text">My self-introduction</p>
            </div>
            <div class="leftBot_text">
                <span>01</span>
                <p>Player No. 251917 of Earth OnLine</p>
            </div>
        </div>
    </section>
    <!-- 第二幕，school -->
    <section class="second_plane plane">
        <div style="padding: 50px; color: white; text-align: center;z-index: 100;">
            <h2>滚动测试 - Baby模型移动</h2>
            <p>向下滚动，观察Baby模型向左移动</p>
            <p>打开控制台查看滚动进度和位置变化</p>
        </div>
    </section>

    <!-- 增加更多内容确保有足够的滚动距离 -->
    <section style="height: 100vh; background: rgba(0,0,0,0.3); color: white; padding: 50px; z-index: 100; position: relative;">
        <h2>第一段滚动内容</h2>
        <p>继续滚动...</p>
    </section>

    <section style="height: 100vh; background: rgba(0,0,0,0.2); color: white; padding: 50px; z-index: 100; position: relative;">
        <h2>第二段滚动内容</h2>
        <p>继续滚动...</p>
    </section>

    <section style="height: 100vh; background: rgba(0,0,0,0.1); color: white; padding: 50px; z-index: 100; position: relative;">
        <h2>第三段滚动内容</h2>
        <p>滚动到底部</p>
    </section>

    <!-- babylon initDom -->
    <canvas id="babylonRenderCanvas" style="width:100%;height: 100%;position: fixed;left:0;top:0;z-index: 1;"> </canvas>
    <!-- GSAP -->
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/ScrollTrigger.min.js"></script>
    <!-- babylon -->
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.js"></script>
    <script src="https://cdn.babylonjs.com/earcut.min.js"></script>
    <!-- ENTRY -->
    <script type="module" src="./js/index.js"></script>
</body>

</html>